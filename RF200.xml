<?xml version="1.0" encoding="utf-8"?>
<EtherCATInfo xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="EtherCATInfo.xsd" Version="1.6">
	<Vendor>
		<Id>#x00000130</Id>
		<Name>Jihua Laboratory Intelligent Robot Engineering Research Center</Name>
		<ImageData16x14>424DD6020000000000003600000028000000100000000E000000010018000000000000000000C40E0000C40E00000000000000000000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFCFAF6F2E6D6F1E6D5F1E6D5F6EFE4FFFFFFF6EFE4F6EEE3F7F0E6F3E9D9FFFFFFF8F4EBF2E7D6F1E7D6F2E7D6FAF6F0FAF6F1C28C3CBC7E24BA7A1EBD8028F7F1E8C08632D1A86CD2A96DBF8631FAF7F1BB7E24B97717BC7E23C08835FBF9F6FFFFFFFCF9F5FBF9F5E2C9A3B77514F1E5D4C08532C8974FC48F41BF8631F1E5D4B77414E0C7A0FBF9F5FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE4CBA7B77514F1E5D4C08532C18734C08631BF8631F1E5D4B77414E5CFAFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE4CBA7B77514F1E5D3C08532D1A86DD2A96DBF8531F0E5D4B77414E5CFAFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF2E7D7E5CEADF9F5EEE6D0B0F1E6D6ECDDC6E9D7BCF5EEE2E4CEADF7F2E9FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF</ImageData16x14>
	</Vendor>
	<Descriptions>
		<Groups>
            <Group SortOrder="0">
                <Type>Renesas Slave</Type>
                <Name LcId="1033">RF200 Series Slaves</Name>
				<ImageData16x14>424DD6020000000000003600000028000000100000000E000000010018000000000000000000C40E0000C40E00000000000000000000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFCFAF6F2E6D6F1E6D5F1E6D5F6EFE4FFFFFFF6EFE4F6EEE3F7F0E6F3E9D9FFFFFFF8F4EBF2E7D6F1E7D6F2E7D6FAF6F0FAF6F1C28C3CBC7E24BA7A1EBD8028F7F1E8C08632D1A86CD2A96DBF8631FAF7F1BB7E24B97717BC7E23C08835FBF9F6FFFFFFFCF9F5FBF9F5E2C9A3B77514F1E5D4C08532C8974FC48F41BF8631F1E5D4B77414E0C7A0FBF9F5FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE4CBA7B77514F1E5D4C08532C18734C08631BF8631F1E5D4B77414E5CFAFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE4CBA7B77514F1E5D3C08532D1A86DD2A96DBF8531F0E5D4B77414E5CFAFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF2E7D7E5CEADF9F5EEE6D0B0F1E6D6ECDDC6E9D7BCF5EEE2E4CEADF7F2E9FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
				</ImageData16x14>				
			</Group>
		</Groups>
		<Devices>
			<Device Physics="YY">
				<Type ProductCode="#x01300007" RevisionNo="#x0100">RF200</Type>
				<Name LcId="1033">RF200 EtherCAT Slave</Name>
				<Info>
					<StateMachine>
						<Timeout>
							<PreopTimeout>2000</PreopTimeout>
							<SafeopOpTimeout>9000</SafeopOpTimeout>
							<BackToInitTimeout>5000</BackToInitTimeout>
							<BackToSafeopTimeout>200</BackToSafeopTimeout>
						</Timeout>
					</StateMachine>
					<Mailbox>
						<Timeout>
							<RequestTimeout>100</RequestTimeout>
							<ResponseTimeout>5000</ResponseTimeout>
						</Timeout>
					</Mailbox>
				</Info>
				<GroupType>Renesas Slave</GroupType>
				<Profile>
					<ProfileNo>402</ProfileNo>
					<AddInfo>0</AddInfo>
					<Dictionary>
						<DataTypes>
							<DataType>
								<Name>STRING(3)</Name>
								<BitSize>24</BitSize>
							</DataType>
							<DataType>
								<Name>USINT</Name>
								<BitSize>8</BitSize>
							</DataType>
							<DataType>
								<Name>UDINT</Name>
								<BitSize>32</BitSize>
							</DataType>
							<DataType>
								<Name>UINT</Name>
								<BitSize>16</BitSize>
							</DataType>
							<DataType>
								<Name>ULINT</Name>
								<BitSize>64</BitSize>
							</DataType>
							<DataType>
								<Name>BOOL</Name>
								<BitSize>1</BitSize>
							</DataType>
							<DataType>
								<Name>INT</Name>
								<BitSize>16</BitSize>
							</DataType>
							<DataType>
								<Name>SINT</Name>
								<BitSize>8</BitSize>
							</DataType>
							<DataType>
								<Name>DINT</Name>
								<BitSize>32</BitSize>
							</DataType>
							<DataType>
								<Name>DT1018</Name>
								<BitSize>144</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Vendor ID</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Product Code</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Revision Number</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Serial number</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT10F1</Name>
								<BitSize>64</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Local Error Reaction</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Sync Error Counter Limit</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1600</Name>
								<BitSize>336</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>RxPdo1</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>RxPdo2</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>RxPdo3</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>RxPdo4</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>RxPdo5</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>RxPdo6</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>RxPdo7</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>RxPdo8</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>RxPdo9</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>272</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>RxPdo10</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1A00</Name>
								<BitSize>336</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>TxPdo1</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>TxPdo2</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>TxPdo3</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>TxPdo4</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>TxPdo5</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>TxPdo6</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>TxPdo7</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>TxPdo8</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>TxPdo9</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>272</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>TxPdo10</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1C00ARR</Name>
								<BaseType>USINT</BaseType>
								<BitSize>32</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>4</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1C00</Name>
								<BitSize>48</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1C00ARR</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1C12ARR</Name>
								<BaseType>UINT</BaseType>
								<BitSize>16</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>1</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1C12</Name>
								<BitSize>32</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1C12ARR</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1C13ARR</Name>
								<BaseType>UINT</BaseType>
								<BitSize>16</BitSize>
								<ArrayInfo>
									<LBound>1</LBound>
									<Elements>1</Elements>
								</ArrayInfo>
							</DataType>
							<DataType>
								<Name>DT1C13</Name>
								<BitSize>32</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<Name>Elements</Name>
									<Type>DT1C13ARR</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1C32</Name>
								<BitSize>488</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Synchronization Type</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Cycle Time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Synchronization Types supported</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Minimum Cycle Time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Calc and Copy Time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>Get Cycle Time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>Delay Time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>224</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>Sync0 Cycle Time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>256</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>SM-Event Missed</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>288</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>Cycle Time Too Small</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>13</SubIdx>
									<Name>Shift Time Too Short Counter</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>320</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>32</SubIdx>
									<Name>Sync Error</Name>
									<Type>BOOL</Type>
									<BitSize>1</BitSize>
									<BitOffs>480</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT1C33</Name>
								<BitSize>488</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Synchronization Type</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access WriteRestrictions="PreOP">rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Cycle Time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Synchronization Types supported</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Minimum Cycle Time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Calc and Copy Time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>Get Cycle Time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>Delay Time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>224</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>Sync0 Cycle Time</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>256</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>SM-Event Missed</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>288</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>Cycle Time Too Small</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>13</SubIdx>
									<Name>Shift Time Too Short Counter</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>320</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>32</SubIdx>
									<Name>Sync Error</Name>
									<Type>BOOL</Type>
									<BitSize>1</BitSize>
									<BitOffs>480</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT2000</Name>
								<BitSize>464</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Motor Type</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Motor Rated Frequency   </Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Motor Rated Power</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Motor Rated Voltage</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Motor Rated Current</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Motor Rated Torque</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>Motor Rated Speed</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>Motor Max Current</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>128</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>Motor Max Torque</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>Motor Max Speed</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>160</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>Motor Pole Pairs Number</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>Motor Winding Resistance</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>192</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>13</SubIdx>
									<Name>Motor Winding Inductance</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>14</SubIdx>
									<Name>Motor Rotor Inertia</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>224</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>15</SubIdx>
									<Name>Motor Back EMF</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>16</SubIdx>
									<Name>Motor Torque Constant</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>256</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>17</SubIdx>
									<Name>Motor Mechanical Constant</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>272</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>18</SubIdx>
									<Name>Encoder Type</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>288</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>19</SubIdx>
									<Name>ABS Encoder Multi-turn Bit</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>20</SubIdx>
									<Name>ABS Encoder Single-turn Bit</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>320</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>21</SubIdx>
									<Name>ABS Encoder offset</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>336</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>22</SubIdx>
									<Name>ABZ Encoder Pules</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>368</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>23</SubIdx>
									<Name>Motor Inductance Ld</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>400</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>24</SubIdx>
									<Name>Motor Inductance Lq</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>416</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>39</SubIdx>
									<Name>Linear Motor Pitch</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>432</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>40</SubIdx>
									<Name>Bissc data filed length</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>448</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT2001</Name>
								<BitSize>720</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Device Type</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Device Voltage Grade</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Device Rated Power</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Device Max Power</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Device Rated Current</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Device Max Current</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>Rotate Direction</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>Over Travel Stop Mode</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>128</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>Force Stop Mode</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>Alarm Stop Mode</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>160</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>Fault NO.1 Stop Mode</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>Brake Release Delay Time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>192</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>13</SubIdx>
									<Name>Brake Active Delay Time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>14</SubIdx>
									<Name>Brake Active Velocity</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>224</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>15</SubIdx>
									<Name>Brake Active Allowed Delay Time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>16</SubIdx>
									<Name>Brake Enable</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>256</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>17</SubIdx>
									<Name>LED Warn Select</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>272</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>18</SubIdx>
									<Name>Regen Resistor Min Value</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>288</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>19</SubIdx>
									<Name>Regen Resistor Type</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>20</SubIdx>
									<Name>Res Heat Dissipation Coeff</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>320</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>21</SubIdx>
									<Name>Ext Regen Resistor Power</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>336</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>22</SubIdx>
									<Name>Ext Regen Resistor Value</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>352</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>23</SubIdx>
									<Name>System Init</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>368</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>24</SubIdx>
									<Name>DcBus Input Select</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>384</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>25</SubIdx>
									<Name>Ac Off Detect Time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>400</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>26</SubIdx>
									<Name>Power Charge Wait Time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>416</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>27</SubIdx>
									<Name>Power Ready Wait Time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>432</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>28</SubIdx>
									<Name>Over Load Warn Level</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>448</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>29</SubIdx>
									<Name>Motor OL Detect Current Derate</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>464</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>30</SubIdx>
									<Name>Motor line UVW sequence</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>480</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>31</SubIdx>
									<Name>Ac Off Discharge Switch</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>496</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>32</SubIdx>
									<Name>Servo Version</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>512</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>33</SubIdx>
									<Name>Phase Offset Iu</Name>
									<Type>INT</Type>
									<BitSize>16</BitSize>
									<BitOffs>544</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>34</SubIdx>
									<Name>Phase Offset Iv</Name>
									<Type>INT</Type>
									<BitSize>16</BitSize>
									<BitOffs>560</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>41</SubIdx>
									<Name>Device ID</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>576</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>48</SubIdx>
									<Name>Bus Frame set</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>592</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>49</SubIdx>
									<Name>Openloop type</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>608</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>50</SubIdx>
									<Name>OpenLoop alpha phase voltage</Name>
									<Type>INT</Type>
									<BitSize>16</BitSize>
									<BitOffs>624</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>51</SubIdx>
									<Name>OpenLoop beta phase voltage</Name>
									<Type>INT</Type>
									<BitSize>16</BitSize>
									<BitOffs>640</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>52</SubIdx>
									<Name>OpenLoop Electric Angle</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>656</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>53</SubIdx>
									<Name>Bus Type</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>672</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>54</SubIdx>
									<Name>Pluse input Mode select</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>688</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>55</SubIdx>
									<Name>Motor prm init select</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>704</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT2002</Name>
								<BitSize>1488</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Control Source Option</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Position Loop Gain</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>rw</Access>
										<PdoMapping>r</PdoMapping>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Speed Loop Gain</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<PdoMapping>r</PdoMapping>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Speed Loop Time Constant</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>rw</Access>
										<PdoMapping>r</PdoMapping>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Load Inertia Ratio</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Speed Loop Gain 2</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>Speed Loop Time Constant 2</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>Speed PDFF Coff</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>128</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>Current Loop Gain</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>Speed Mode Switch</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>160</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>Mode Switch Torque Value</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>Mode Switch Speed Value</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>192</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>13</SubIdx>
									<Name>Mode Switch Acc Value</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>14</SubIdx>
									<Name>Mode Switch PosErr Value</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>224</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>15</SubIdx>
									<Name>TrqFF Control Select</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>16</SubIdx>
									<Name>TrqFF Filter Fime Constant</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>256</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>17</SubIdx>
									<Name>TrqFF Gain</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>272</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>18</SubIdx>
									<Name>SpdFF Control Select</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>288</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>19</SubIdx>
									<Name>SpdFF Filter Fime Constant</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>20</SubIdx>
									<Name>SpdFF Gain</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>320</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>21</SubIdx>
									<Name>Servo On Speed Limit</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>336</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>22</SubIdx>
									<Name>TrqCtrl Speed Limit</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>352</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>23</SubIdx>
									<Name>Speed Average Filter Config</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>368</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>24</SubIdx>
									<Name>First TrqCmd Filter Time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>384</BitOffs>
									<Flags>
										<Access>rw</Access>
										<PdoMapping>r</PdoMapping>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>25</SubIdx>
									<Name>Second TrqCmd Filter Freq</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>400</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>26</SubIdx>
									<Name>Second TrqCmd Filter Q </Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>416</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>27</SubIdx>
									<Name>Forward Internal Torque Limit</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>432</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>28</SubIdx>
									<Name>Reverse Internal Torque Limit</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>448</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>29</SubIdx>
									<Name>Forward External Torque Limit</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>464</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>30</SubIdx>
									<Name>Reverse External Torque Limit</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>480</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>31</SubIdx>
									<Name>Emergency Stop Torque Limit</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>496</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>32</SubIdx>
									<Name>Phase Find Method</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>512</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>33</SubIdx>
									<Name>Phase Find Ramp Time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>528</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>34</SubIdx>
									<Name>Phase Find Stabilize Time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>544</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>35</SubIdx>
									<Name>Phase Find Current </Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>560</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>36</SubIdx>
									<Name>Function Generater Type</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>576</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>37</SubIdx>
									<Name>Function Generater Number</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>592</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>38</SubIdx>
									<Name>Function Generater Frequency</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>608</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>39</SubIdx>
									<Name>Function Generater Amplitude</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>624</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>40</SubIdx>
									<Name>Speed Reference LPF Time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>656</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>41</SubIdx>
									<Name>Notch Filter Config</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>672</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>42</SubIdx>
									<Name>Notch filter frequency 1</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>704</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>43</SubIdx>
									<Name>Notch filter Q factor 1</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>720</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>44</SubIdx>
									<Name>Notch filter Depth 1 </Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>736</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>45</SubIdx>
									<Name>Notch filter frequency 2</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>752</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>46</SubIdx>
									<Name>Notch filter Q factor 2</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>768</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>47</SubIdx>
									<Name>Notch filter Depth 2</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>784</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>48</SubIdx>
									<Name>Notch filter frequency 3</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>800</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>49</SubIdx>
									<Name>Notch filter Q factor 3</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>816</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>50</SubIdx>
									<Name>Notch filter Depth 3</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>832</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>51</SubIdx>
									<Name>Notch filter frequency 4</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>848</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>52</SubIdx>
									<Name>Notch filter Q factor 4</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>864</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>53</SubIdx>
									<Name>Notch filter Depth 4</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>880</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>54</SubIdx>
									<Name>Speed Feedback LPF Time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>896</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>55</SubIdx>
									<Name>Inertia Identification Accelerate Time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>912</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>56</SubIdx>
									<Name>Inertia Identification Position</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>928</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>57</SubIdx>
									<Name>Current loop Ti</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>960</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>58</SubIdx>
									<Name>Function Generator Slope</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>976</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>59</SubIdx>
									<Name>Position Reference Ma Filter Time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>1008</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>60</SubIdx>
									<Name>Speed feedforward Ma Filter Time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>1024</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>61</SubIdx>
									<Name>Torque feedforward Ma Filter Time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>1040</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>62</SubIdx>
									<Name>Position Reference high filter ratio</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>1056</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>63</SubIdx>
									<Name>Position Reference Exp Filter Time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>1072</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>64</SubIdx>
									<Name>Program Jog switch</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>1088</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>65</SubIdx>
									<Name>Program Jog moving distance</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>1104</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>66</SubIdx>
									<Name>Program Jog moving speed</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>1136</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>67</SubIdx>
									<Name>Program Jog Acc/Dec time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>1152</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>68</SubIdx>
									<Name>Program Jog wait time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>1168</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>69</SubIdx>
									<Name>Program Jog moving number</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>1184</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>70</SubIdx>
									<Name>Jog Speed</Name>
									<Type>INT</Type>
									<BitSize>16</BitSize>
									<BitOffs>1200</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>71</SubIdx>
									<Name>Jog Acceleration time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>1216</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>72</SubIdx>
									<Name>Jog Deceleration time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>1232</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>73</SubIdx>
									<Name>Gain Change Time 1</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>1248</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>74</SubIdx>
									<Name>Gain Change Time 2</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>1264</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>75</SubIdx>
									<Name>Gain Change Wait Time 1</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>1280</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>76</SubIdx>
									<Name>Gain Change Wait Time 2</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>1296</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>77</SubIdx>
									<Name>Gain Change Switch</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>1312</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>78</SubIdx>
									<Name>Speed loop pdff control coff</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>1328</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>79</SubIdx>
									<Name>First TrqCmd Filter Time 2</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>1344</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>80</SubIdx>
									<Name>Gravity Torque</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>1360</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>81</SubIdx>
									<Name>Static Friction Positive Torque</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>1376</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>82</SubIdx>
									<Name>Static Friction Negetive Torque</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>1392</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>83</SubIdx>
									<Name>Viscosity Torque  </Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>1408</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>84</SubIdx>
									<Name>Friction Speed</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>1424</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>85</SubIdx>
									<Name>Position Disturbance Compensation</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>1440</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>89</SubIdx>
									<Name>Jog time</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>1472</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT2003</Name>
								<BitSize>704</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Vibration Suppression Option</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>VibSup Freq</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>VibSup Gain Comp</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>VibSup Damping Gain</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>VibSup Filter Time1 Comp</Name>
									<Type>INT</Type>
									<BitSize>16</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>VibSup Filter Time2 Comp</Name>
									<Type>INT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>VubSup Damping Gain2</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>VibSup Freq2</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>128</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>Disturbance observer gain</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>Disturbance observer gain 2</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>160</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>Disturbance observer coefficient</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>Disturbance observer freq correction</Name>
									<Type>INT</Type>
									<BitSize>16</BitSize>
									<BitOffs>192</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>13</SubIdx>
									<Name>Disturbance observer gain correction</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>14</SubIdx>
									<Name>Speed observer gain</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>224</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>15</SubIdx>
									<Name>Speed observer pos compensation gain</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>16</SubIdx>
									<Name>Advanced Application Switch</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>256</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>17</SubIdx>
									<Name>End Vibration Suppression Option</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>272</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>18</SubIdx>
									<Name>End Vibration Suppression Frequency</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>288</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>19</SubIdx>
									<Name>End Vibration Suppression Compensation</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>20</SubIdx>
									<Name>Tuneless Setting</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>320</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>21</SubIdx>
									<Name>Inertia Identification Start Value</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>336</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>22</SubIdx>
									<Name>Automatic Notch Filter Switch</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>352</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>23</SubIdx>
									<Name>Model Following Control Switch</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>368</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>24</SubIdx>
									<Name>Model Following Control Gain</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>384</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>25</SubIdx>
									<Name>MFC Gain Correction</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>400</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>26</SubIdx>
									<Name>MFC Forward Bias</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>416</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>27</SubIdx>
									<Name>MFC Reverse Bias</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>432</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>28</SubIdx>
									<Name>Vibration Suppression 1 Frequency A</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>448</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>29</SubIdx>
									<Name>Vibration Suppression 1 Frequency B</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>464</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>30</SubIdx>
									<Name>MFC Velocity Feedforward Compensation</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>480</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>31</SubIdx>
									<Name>MFC Gain 2</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>496</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>32</SubIdx>
									<Name>MFC Gain Correction 2</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>512</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>33</SubIdx>
									<Name>Weak Field Control Gain</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>528</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>34</SubIdx>
									<Name>Weak Field Control Time Constant</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>544</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>35</SubIdx>
									<Name>Weak Field Max Speed Corresponding To IdRef</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>560</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>36</SubIdx>
									<Name>Weak Field Control Switch</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>576</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>39</SubIdx>
									<Name>AatJratEst</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>592</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>40</SubIdx>
									<Name>AatMode</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>608</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>41</SubIdx>
									<Name>AatDistance</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>624</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>42</SubIdx>
									<Name>AatLoadType</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>656</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>43</SubIdx>
									<Name>AatState</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>672</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>44</SubIdx>
									<Name>MotEstState</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>688</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT2004</Name>
								<BitSize>208</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>DI1 Fuction Select</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>DI1 Logic Select</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>DI2 Fuction Select</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>DI2 Logic Select</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>DI3 Fuction Select</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>DI3 Logic Select</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>DI4 Fuction Select</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>DI4 Logic Select</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>128</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>DI5 Fuction Select</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>DI5 Logic Select</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>160</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>DI6 Fuction Select</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>DI6 Logic Select</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>192</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT2005</Name>
								<BitSize>208</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>DO1 Fuction Select</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>DO1 Logic Select</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>DO2 Fuction Select</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>DO2 Logic Select</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>DO3 Fuction Select</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>DO3 Logic Select</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>DO4 Fuction Select</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>DO4 Logic Select</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>128</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>DO5 Fuction Select</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>DO5 Logic Select</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>160</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>DO6 Fuction Select</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>DO6 Logic Select</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>192</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT2006</Name>
								<BitSize>496</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Vdc OV Level</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Vdc Discharge Level</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Vdc Uv Level</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Vdc Uv Filter</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Vdc Uv Warn Level</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Over Speed Value</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>PosErr Warn Level</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>PosErr Alarm Level</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>Svon PosErr Warn Level</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>Svon PosErr Alarm Level</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>Over Run Detection Torque</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>240</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>12</SubIdx>
									<Name>Timestamp</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>256</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>13</SubIdx>
									<Name>Alarm Mask</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>288</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>14</SubIdx>
									<Name>Vibration Detection Sensitivity</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>304</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>15</SubIdx>
									<Name>Vibration Detection Value</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>320</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>16</SubIdx>
									<Name>Low Freq Vibration Detection Value </Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>336</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>17</SubIdx>
									<Name>Vibration Detection Switch</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>352</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>18</SubIdx>
									<Name>Remain Vibration Detection Width</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>368</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>19</SubIdx>
									<Name>Position completion width</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>384</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>21</SubIdx>
									<Name>Alarm Cache chennal 1 setting</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>416</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>22</SubIdx>
									<Name>Alarm Cache chennal 2 setting</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>448</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>23</SubIdx>
									<Name>Alarm Cache chennal Time setting</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>480</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT2007</Name>
								<BitSize>224</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>System Reset</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Factory Reset</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>32</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Fault Reset</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Clear Error History</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>64</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Phase Find Start</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Save Prm to Eeprom</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>96</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>7</SubIdx>
									<Name>Load Prm from Eeprom</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>8</SubIdx>
									<Name>Function Generater Start</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>128</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>9</SubIdx>
									<Name>Fn Servo Off</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>10</SubIdx>
									<Name>Motor Program Jog Command</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>160</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>11</SubIdx>
									<Name>Motor Jog Command</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>13</SubIdx>
									<Name>Get Motor Encoder Command</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>192</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>14</SubIdx>
									<Name>Auto Tunning</Name>
									<Type>UINT</Type>
									<BitSize>16</BitSize>
									<BitOffs>208</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT607D</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Min software position limit</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<PdoMapping>r</PdoMapping>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Max software position limit</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<PdoMapping>r</PdoMapping>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT6091</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Motor Revolutions</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<PdoMapping>r</PdoMapping>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Load Shaft Revolutions</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<PdoMapping>r</PdoMapping>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT6092</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Feed</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<PdoMapping>t</PdoMapping>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Shaft Revolutions</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<PdoMapping>t</PdoMapping>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT6099</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Fast Homing Speed</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<PdoMapping>r</PdoMapping>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Slow Homing Speed</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<PdoMapping>r</PdoMapping>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT60A4</Name>
								<BitSize>208</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Profile Jerk 1</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Profile Jerk 2</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>3</SubIdx>
									<Name>Profile Jerk 3</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>80</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>4</SubIdx>
									<Name>Profile Jerk 4</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>112</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>5</SubIdx>
									<Name>Profile Jerk 5</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>144</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>6</SubIdx>
									<Name>Profile Jerk 6</Name>
									<Type>DINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>176</BitOffs>
									<Flags>
										<Access>rw</Access>
									</Flags>
								</SubItem>
							</DataType>
							<DataType>
								<Name>DT60FE</Name>
								<BitSize>80</BitSize>
								<SubItem>
									<SubIdx>0</SubIdx>
									<Name>SubIndex 000</Name>
									<Type>USINT</Type>
									<BitSize>8</BitSize>
									<BitOffs>0</BitOffs>
									<Flags>
										<Access>ro</Access>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>1</SubIdx>
									<Name>Physical Output</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>16</BitOffs>
									<Flags>
										<Access>rw</Access>
										<PdoMapping>r</PdoMapping>
									</Flags>
								</SubItem>
								<SubItem>
									<SubIdx>2</SubIdx>
									<Name>Bit Mask</Name>
									<Type>UDINT</Type>
									<BitSize>32</BitSize>
									<BitOffs>48</BitOffs>
									<Flags>
										<Access>rw</Access>
										<PdoMapping>r</PdoMapping>
									</Flags>
								</SubItem>
							</DataType>
						</DataTypes>
						<Objects>
							<Object>
								<Index>#x1000</Index>
								<Name>Device type</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<DefaultData>92010000</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x1001</Index>
								<Name>Error register</Name>
								<Type>USINT</Type>
								<BitSize>8</BitSize>
								<Info>
									<DefaultData>00</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x1008</Index>
								<Name>Device name</Name>
								<Type>STRING(3)</Type>
								<BitSize>24</BitSize>
								<Info>
									<DefaultData>617070</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x1009</Index>
								<Name>Manufacturer Hardware version</Name>
								<Type>STRING(3)</Type>
								<BitSize>24</BitSize>
								<Info>
									<DefaultData>312E30</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x100A</Index>
								<Name>Manufacturer Software version</Name>
								<Type>STRING(3)</Type>
								<BitSize>24</BitSize>
								<Info>
									<DefaultData>312E30</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x1018</Index>
								<Name>Identity Object</Name>
								<Type>DT1018</Type>
								<BitSize>144</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>04</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Vendor ID</Name>
										<Info>
											<DefaultData>880A0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Product Code</Name>
										<Info>
											<DefaultData>6000880A</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Revision Number</Name>
										<Info>
											<DefaultData>01000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Serial number</Name>
										<Info>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
							</Object>
							<Object>
								<Index>#x10F1</Index>
								<Name>Error Settings</Name>
								<Type>DT10F1</Type>
								<BitSize>64</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Local Error Reaction</Name>
										<Info>
											<DefaultData>01000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Sync Error Counter Limit</Name>
										<Info>
											<DefaultData>0400</DefaultData>
										</Info>
									</SubItem>
								</Info>
							</Object>
							<!--
							<Object>
								<Index>#x10F8</Index>
								<Name>Timestamp Object</Name>
								<Type>ULINT</Type>
								<BitSize>64</BitSize>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>t</PdoMapping>
								</Flags>
							</Object>
							-->
							<Object>
								<Index>#x1600</Index>
								<Name>RxPdo1 Axis A</Name>
								<Type>DT1600</Type>
								<BitSize>336</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>0A</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>RxPdo1</Name>
										<Info>
											<MinData>00000000</MinData>
											<MaxData>FFFFFFFF</MaxData>
											<DefaultData>10004060</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>RxPdo2</Name>
										<Info>
											<MinData>00000000</MinData>
											<MaxData>FFFFFFFF</MaxData>
											<DefaultData>20007A60</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>RxPdo3</Name>
										<Info>
											<MinData>00000000</MinData>
											<MaxData>FFFFFFFF</MaxData>
											<DefaultData>2000B160</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>RxPdo4</Name>
										<Info>
											<MinData>00000000</MinData>
											<MaxData>FFFFFFFF</MaxData>
											<DefaultData>1000B260</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>RxPdo5</Name>
										<Info>
											<MinData>00000000</MinData>
											<MaxData>FFFFFFFF</MaxData>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>RxPdo6</Name>
										<Info>
											<MinData>00000000</MinData>
											<MaxData>FFFFFFFF</MaxData>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>RxPdo7</Name>
										<Info>
											<MinData>00000000</MinData>
											<MaxData>FFFFFFFF</MaxData>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>RxPdo8</Name>
										<Info>
											<MinData>00000000</MinData>
											<MaxData>FFFFFFFF</MaxData>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>RxPdo9</Name>
										<Info>
											<MinData>00000000</MinData>
											<MaxData>FFFFFFFF</MaxData>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>RxPdo10</Name>
										<Info>
											<MinData>00000000</MinData>
											<MaxData>FFFFFFFF</MaxData>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
							</Object>
							<Object>
								<Index>#x1A00</Index>
								<Name>TxPdo1 Axis A</Name>
								<Type>DT1A00</Type>
								<BitSize>336</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>0A</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>TxPdo1</Name>
										<Info>
											<MinData>00000000</MinData>
											<MaxData>FFFFFFFF</MaxData>
											<DefaultData>10004160</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>TxPdo2</Name>
										<Info>
											<MinData>00000000</MinData>
											<MaxData>FFFFFFFF</MaxData>
											<DefaultData>20006460</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>TxPdo3</Name>
										<Info>
											<MinData>00000000</MinData>
											<MaxData>FFFFFFFF</MaxData>
											<DefaultData>20006C60</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>TxPdo4</Name>
										<Info>
											<MinData>00000000</MinData>
											<MaxData>FFFFFFFF</MaxData>
											<DefaultData>10007760</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>TxPdo5</Name>
										<Info>
											<MinData>00000000</MinData>
											<MaxData>FFFFFFFF</MaxData>
											<DefaultData>08006160</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>TxPdo6</Name>
										<Info>
											<MinData>00000000</MinData>
											<MaxData>FFFFFFFF</MaxData>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>TxPdo7</Name>
										<Info>
											<MinData>00000000</MinData>
											<MaxData>FFFFFFFF</MaxData>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>TxPdo8</Name>
										<Info>
											<MinData>00000000</MinData>
											<MaxData>FFFFFFFF</MaxData>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>TxPdo9</Name>
										<Info>
											<MinData>00000000</MinData>
											<MaxData>FFFFFFFF</MaxData>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>TxPdo10</Name>
										<Info>
											<MinData>00000000</MinData>
											<MaxData>FFFFFFFF</MaxData>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
							</Object>
							<Object>
								<Index>#x1C00</Index>
								<Name>Sync manager type</Name>
								<Type>DT1C00</Type>
								<BitSize>48</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>04</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 001</Name>
										<Info>
											<DefaultData>01</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 002</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 003</Name>
										<Info>
											<DefaultData>03</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SubIndex 004</Name>
										<Info>
											<DefaultData>04</DefaultData>
										</Info>
									</SubItem>
								</Info>
							</Object>
							<Object>
								<Index>#x1C12</Index>
								<Name>SyncManager 2 assignment</Name>
								<Type>DT1C12</Type>
								<BitSize>32</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>01</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Assign1</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0016</DefaultData>
										</Info>
									</SubItem>
								</Info>
							</Object>
							<Object>
								<Index>#x1C13</Index>
								<Name>SyncManager 3 assignment</Name>
								<Type>DT1C13</Type>
								<BitSize>32</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>01</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Assign1</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>001A</DefaultData>
										</Info>
									</SubItem>
								</Info>
							</Object>
							<Object>
								<Index>#x1C32</Index>
								<Name>SM output parameter</Name>
								<Type>DT1C32</Type>
								<BitSize>488</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>20</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Synchronization Type</Name>
										<Info>
											<DefaultData>0100</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Synchronization Types supported</Name>
										<Info>
											<DefaultData>0780</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Minimum Cycle Time</Name>
										<Info>
											<DefaultData>20A10700</DefaultData>
										</Info>
									</SubItem>
								</Info>
							</Object>
							<Object>
								<Index>#x1C33</Index>
								<Name>SM input parameter</Name>
								<Type>DT1C33</Type>
								<BitSize>488</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>20</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Synchronization Type</Name>
										<Info>
											<DefaultData>2200</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Synchronization Types supported</Name>
										<Info>
											<DefaultData>0780</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Minimum Cycle Time</Name>
										<Info>
											<DefaultData>20A10700</DefaultData>
										</Info>
									</SubItem>
								</Info>
							</Object>
							<Object>
								<Index>#x2000</Index>
								<Name>Axis A Motor Parameters</Name>
								<Type>DT2000</Type>
								<BitSize>464</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>28</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Motor Type</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Motor Rated Frequency   </Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Motor Rated Power</Name>
										<Info>
											<MinData>0100</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>1400</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Motor Rated Voltage</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0100</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Motor Rated Current</Name>
										<Info>
											<MinData>0100</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>1200</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Motor Rated Torque</Name>
										<Info>
											<MinData>0A00</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>4000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Motor Rated Speed</Name>
										<Info>
											<MinData>6400</MinData>
											<MaxData>7017</MaxData>
											<DefaultData>B80B</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Motor Max Current</Name>
										<Info>
											<MinData>0100</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>3300</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Motor Max Torque</Name>
										<Info>
											<MinData>0A00</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>BF00</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Motor Max Speed</Name>
										<Info>
											<MinData>6400</MinData>
											<MaxData>7017</MaxData>
											<DefaultData>3C0F</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Motor Pole Pairs Number</Name>
										<Info>
											<MinData>0100</MinData>
											<MaxData>6801</MaxData>
											<DefaultData>0400</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Motor Winding Resistance</Name>
										<Info>
											<MinData>0100</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>4C1D</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Motor Winding Inductance</Name>
										<Info>
											<MinData>0100</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>6009</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Motor Rotor Inertia</Name>
										<Info>
											<MinData>0100</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>1B00</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Motor Back EMF</Name>
										<Info>
											<MinData>0100</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0410</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Motor Torque Constant</Name>
										<Info>
											<MinData>0100</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>2700</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Motor Mechanical Constant</Name>
										<Info>
											<MinData>0100</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>4001</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Encoder Type</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0200</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>ABS Encoder Multi-turn Bit</Name>
										<Info>
											<MinData>0100</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>1000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>ABS Encoder Single-turn Bit</Name>
										<Info>
											<MinData>0100</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>1100</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>ABS Encoder offset</Name>
										<Info>
											<MinData>00000000</MinData>
											<MaxData>FFFFFFFF</MaxData>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>ABZ Encoder Pules</Name>
										<Info>
											<MinData>00000000</MinData>
											<MaxData>FFFFFFFF</MaxData>
											<DefaultData>10270000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Motor Inductance Ld</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>B004</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Motor Inductance Lq</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>B004</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Linear Motor Pitch</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>1027</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Bissc data filed length</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>2400</DefaultData>
										</Info>
									</SubItem>
								</Info>
							</Object>
							<Object>
								<Index>#x2001</Index>
								<Name>Axis A Basic Configuration Parameters</Name>
								<Type>DT2001</Type>
								<BitSize>720</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>37</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Device Type</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0100</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Device Voltage Grade</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>5802</MaxData>
											<DefaultData>7C01</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Device Rated Power</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>E803</MaxData>
											<DefaultData>4B00</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Device Max Power</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>E803</MaxData>
											<DefaultData>9600</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Device Rated Current</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>E803</MaxData>
											<DefaultData>2800</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Device Max Current</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>E803</MaxData>
											<DefaultData>5000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Rotate Direction</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0100</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Over Travel Stop Mode</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0200</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Force Stop Mode</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0200</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Alarm Stop Mode</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0200</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Fault NO.1 Stop Mode</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0200</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Brake Release Delay Time</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>F401</MaxData>
											<DefaultData>FA00</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Brake Active Delay Time</Name>
										<Info>
											<MinData>3200</MinData>
											<MaxData>E803</MaxData>
											<DefaultData>9600</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Brake Active Velocity</Name>
										<Info>
											<MinData>1400</MinData>
											<MaxData>B80B</MaxData>
											<DefaultData>1E00</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Brake Active Allowed Delay Time</Name>
										<Info>
											<MinData>0100</MinData>
											<MaxData>E803</MaxData>
											<DefaultData>F401</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Brake Enable</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0100</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>LED Warn Select</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0100</MaxData>
											<DefaultData>0100</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Regen Resistor Min Value</Name>
										<Info>
											<MinData>0100</MinData>
											<MaxData>E803</MaxData>
											<DefaultData>2800</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Regen Resistor Type</Name>
										<Info>
											<MinData>0100</MinData>
											<MaxData>0300</MaxData>
											<DefaultData>0300</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Res Heat Dissipation Coeff</Name>
										<Info>
											<MinData>0A00</MinData>
											<MaxData>6400</MaxData>
											<DefaultData>1E00</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Ext Regen Resistor Power</Name>
										<Info>
											<MinData>0100</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>2800</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Ext Regen Resistor Value</Name>
										<Info>
											<MinData>0100</MinData>
											<MaxData>E803</MaxData>
											<DefaultData>3000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>System Init</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0200</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>DcBus Input Select</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0100</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Ac Off Detect Time</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Power Charge Wait Time</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Power Ready Wait Time</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Over Load Warn Level</Name>
										<Info>
											<MinData>1400</MinData>
											<DefaultData>1600</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Motor OL Detect Current Derate</Name>
										<Info>
											<MinData>0100</MinData>
											<DefaultData>6400</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Motor line UVW sequence</Name>
										<Info>
											<MinData>0A00</MinData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Ac Off Discharge Switch</Name>
										<Info>
											<MinData>0000</MinData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Servo Version</Name>
										<Info>
											<MinData>00000000</MinData>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Phase Offset Iu</Name>
										<Info>
											<MinData>0080</MinData>
											<MaxData>FF7F</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Phase Offset Iv</Name>
										<Info>
											<MinData>0080</MinData>
											<MaxData>FF7F</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Device ID</Name>
										<Info>
											<MinData>0100</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0100</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Bus Frame set</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0800</MaxData>
											<DefaultData>0500</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Openloop type</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0800</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>OpenLoop alpha phase voltage</Name>
										<Info>
											<MinData>0080</MinData>
											<MaxData>FF7F</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>OpenLoop beta phase voltage</Name>
										<Info>
											<MinData>0080</MinData>
											<MaxData>FF7F</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>OpenLoop Electric Angle</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Bus Type</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0300</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Pluse input Mode select</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0100</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Motor prm init select</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
								</Info>
							</Object>
							<Object>
								<Index>#x2002</Index>
								<Name>Axis A Motion control parameter</Name>
								<Type>DT2002</Type>
								<BitSize>1488</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>59</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Control Source Option</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Position Loop Gain</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>204E</MaxData>
											<DefaultData>9001</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Speed Loop Gain</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>204E</MaxData>
											<DefaultData>9001</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Speed Loop Time Constant</Name>
										<Info>
											<MinData>1900</MinData>
											<MaxData>50C3</MaxData>
											<DefaultData>D007</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Load Inertia Ratio</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>204E</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Speed Loop Gain 2</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>204E</MaxData>
											<DefaultData>9001</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Speed Loop Time Constant 2</Name>
										<Info>
											<MinData>1900</MinData>
											<MaxData>50C3</MaxData>
											<DefaultData>D007</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Speed PDFF Coff</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>6400</MaxData>
											<DefaultData>6400</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Current Loop Gain</Name>
										<Info>
											<MinData>6400</MinData>
											<MaxData>D007</MaxData>
											<DefaultData>E803</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Speed Mode Switch</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0400</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mode Switch Torque Value</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>2003</MaxData>
											<DefaultData>C800</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mode Switch Speed Value</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>1027</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mode Switch Acc Value</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>3075</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Mode Switch PosErr Value</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>1027</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>TrqFF Control Select</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0200</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>TrqFF Filter Fime Constant</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0019</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>TrqFF Gain</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>6400</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SpdFF Control Select</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0200</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SpdFF Filter Fime Constant</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0019</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>SpdFF Gain</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>6400</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Servo On Speed Limit</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>1027</MaxData>
											<DefaultData>B80B</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>TrqCtrl Speed Limit</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>1027</MaxData>
											<DefaultData>B80B</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Speed Average Filter Config</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0400</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>First TrqCmd Filter Time</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>6400</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Second TrqCmd Filter Freq</Name>
										<Info>
											<MinData>6400</MinData>
											<MaxData>8813</MaxData>
											<DefaultData>8813</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Second TrqCmd Filter Q </Name>
										<Info>
											<MinData>3200</MinData>
											<MaxData>6400</MaxData>
											<DefaultData>3200</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Forward Internal Torque Limit</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>2003</MaxData>
											<DefaultData>2C01</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Reverse Internal Torque Limit</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>2003</MaxData>
											<DefaultData>2C01</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Forward External Torque Limit</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>2003</MaxData>
											<DefaultData>6400</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Reverse External Torque Limit</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>2003</MaxData>
											<DefaultData>6400</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Emergency Stop Torque Limit</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>2003</MaxData>
											<DefaultData>2C01</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Phase Find Method</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0100</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Phase Find Ramp Time</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>6400</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Phase Find Stabilize Time</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>6400</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Phase Find Current </Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Function Generater Type</Name>
										<Info>
											<MinData>0100</MinData>
											<MaxData>0300</MaxData>
											<DefaultData>0100</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Function Generater Number</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Function Generater Frequency</Name>
										<Info>
											<MinData>0100</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0A00</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Function Generater Amplitude</Name>
										<Info>
											<MinData>00000000</MinData>
											<MaxData>FFFFFFFF</MaxData>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Speed Reference LPF Time</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Notch Filter Config</Name>
										<Info>
											<MinData>00000000</MinData>
											<MaxData>FFFFFFFF</MaxData>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Notch filter frequency 1</Name>
										<Info>
											<MinData>3200</MinData>
											<MaxData>8813</MaxData>
											<DefaultData>8813</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Notch filter Q factor 1</Name>
										<Info>
											<MinData>3200</MinData>
											<MaxData>E803</MaxData>
											<DefaultData>4600</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Notch filter Depth 1 </Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>8813</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Notch filter frequency 2</Name>
										<Info>
											<MinData>3200</MinData>
											<MaxData>E803</MaxData>
											<DefaultData>8813</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Notch filter Q factor 2</Name>
										<Info>
											<MinData>3200</MinData>
											<MaxData>8813</MaxData>
											<DefaultData>4600</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Notch filter Depth 2</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>E803</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Notch filter frequency 3</Name>
										<Info>
											<MinData>3200</MinData>
											<MaxData>8813</MaxData>
											<DefaultData>8813</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Notch filter Q factor 3</Name>
										<Info>
											<MinData>3200</MinData>
											<MaxData>E803</MaxData>
											<DefaultData>4600</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Notch filter Depth 3</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>8813</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Notch filter frequency 4</Name>
										<Info>
											<MinData>3200</MinData>
											<MaxData>E803</MaxData>
											<DefaultData>8813</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Notch filter Q factor 4</Name>
										<Info>
											<MinData>3200</MinData>
											<MaxData>8813</MaxData>
											<DefaultData>4600</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Notch filter Depth 4</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>E803</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Speed Feedback LPF Time</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Inertia Identification Accelerate Time</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Inertia Identification Position</Name>
										<Info>
											<MinData>00000000</MinData>
											<MaxData>FFFFFF7F</MaxData>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Current loop Ti</Name>
										<Info>
											<MinData>1900</MinData>
											<MaxData>50C3</MaxData>
											<DefaultData>4001</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Function Generator Slope</Name>
										<Info>
											<MinData>00000000</MinData>
											<MaxData>FFFFFFFF</MaxData>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Position Reference Ma Filter Time</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0028</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Speed feedforward Ma Filter Time</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0028</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Torque feedforward Ma Filter Time</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0028</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Position Reference high filter ratio</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>6400</MaxData>
											<DefaultData>6400</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Position Reference Exp Filter Time</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Program Jog switch</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0500</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Program Jog moving distance</Name>
										<Info>
											<MinData>01000000</MinData>
											<MaxData>00000040</MaxData>
											<DefaultData>00800000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Program Jog moving speed</Name>
										<Info>
											<MinData>0100</MinData>
											<MaxData>8813</MaxData>
											<DefaultData>F401</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Program Jog Acc/Dec time</Name>
										<Info>
											<MinData>0200</MinData>
											<MaxData>1027</MaxData>
											<DefaultData>6400</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Program Jog wait time</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>1027</MaxData>
											<DefaultData>6400</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Program Jog moving number</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>E803</MaxData>
											<DefaultData>0100</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Jog Speed</Name>
										<Info>
											<MinData>8FFD</MinData>
											<MaxData>1027</MaxData>
											<DefaultData>F401</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Jog Acceleration time</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>1027</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Jog Deceleration time</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>1027</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Gain Change Time 1</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Gain Change Time 2</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Gain Change Wait Time 1</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Gain Change Wait Time 2</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Gain Change Switch</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Speed loop pdff control coff</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>6400</MaxData>
											<DefaultData>6400</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>First TrqCmd Filter Time 2</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>6400</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Gravity Torque</Name>
										<Info>
											<MinData>18FC</MinData>
											<MaxData>E803</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Static Friction Positive Torque</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>E803</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Static Friction Negetive Torque</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>E803</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Viscosity Torque  </Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>B80B</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Friction Speed</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>B80B</MaxData>
											<DefaultData>0300</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Position Disturbance Compensation</Name>
										<Info>
											<MinData>00000000</MinData>
											<MaxData>00F40100</MaxData>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Jog time</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0A00</DefaultData>
										</Info>
									</SubItem>
								</Info>
							</Object>
							<Object>
								<Index>#x2003</Index>
								<Name>Axis A Advanced Configuration Parameters</Name>
								<Type>DT2003</Type>
								<BitSize>704</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>2C</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Vibration Suppression Option</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>1100</MaxData>
											<DefaultData>1000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>VibSup Freq</Name>
										<Info>
											<MinData>0A00</MinData>
											<MaxData>204E</MaxData>
											<DefaultData>E803</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>VibSup Gain Comp</Name>
										<Info>
											<MinData>0100</MinData>
											<MaxData>E803</MaxData>
											<DefaultData>6400</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>VibSup Damping Gain</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>2C01</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>VibSup Filter Time1 Comp</Name>
										<Info>
											<MinData>18FC</MinData>
											<MaxData>E803</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>VibSup Filter Time2 Comp</Name>
										<Info>
											<MinData>18FC</MinData>
											<MaxData>E803</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>VubSup Damping Gain2</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>E803</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>VibSup Freq2</Name>
										<Info>
											<MinData>0A00</MinData>
											<MaxData>50C3</MaxData>
											<DefaultData>204E</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Disturbance observer gain</Name>
										<Info>
											<MinData>0A00</MinData>
											<MaxData>E803</MaxData>
											<DefaultData>6400</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Disturbance observer gain 2</Name>
										<Info>
											<MinData>0A00</MinData>
											<MaxData>E803</MaxData>
											<DefaultData>6400</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Disturbance observer coefficient</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>6400</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Disturbance observer freq correction</Name>
										<Info>
											<MinData>F0D8</MinData>
											<MaxData>1027</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Disturbance observer gain correction</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>E803</MaxData>
											<DefaultData>6400</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Speed observer gain</Name>
										<Info>
											<MinData>0100</MinData>
											<MaxData>F401</MaxData>
											<DefaultData>2800</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Speed observer pos compensation gain</Name>
										<Info>
											<MinData>0100</MinData>
											<MaxData>E803</MaxData>
											<DefaultData>9600</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Advanced Application Switch</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>End Vibration Suppression Option</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0100</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>End Vibration Suppression Frequency</Name>
										<Info>
											<MinData>0A00</MinData>
											<MaxData>D007</MaxData>
											<DefaultData>2003</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>End Vibration Suppression Compensation</Name>
										<Info>
											<MinData>0A00</MinData>
											<MaxData>E803</MaxData>
											<DefaultData>6400</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Tuneless Setting</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>1127</MaxData>
											<DefaultData>0114</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Inertia Identification Start Value</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>204E</MaxData>
											<DefaultData>2C01</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Automatic Notch Filter Switch</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0101</MaxData>
											<DefaultData>0101</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Model Following Control Switch</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>2111</MaxData>
											<DefaultData>0001</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Model Following Control Gain</Name>
										<Info>
											<MinData>0A00</MinData>
											<MaxData>204E</MaxData>
											<DefaultData>F401</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>MFC Gain Correction</Name>
										<Info>
											<MinData>F401</MinData>
											<MaxData>D007</MaxData>
											<DefaultData>E803</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>MFC Forward Bias</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>1027</MaxData>
											<DefaultData>E803</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>MFC Reverse Bias</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>1027</MaxData>
											<DefaultData>E803</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Vibration Suppression 1 Frequency A</Name>
										<Info>
											<MinData>0A00</MinData>
											<MaxData>C409</MaxData>
											<DefaultData>F401</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Vibration Suppression 1 Frequency B</Name>
										<Info>
											<MinData>0A00</MinData>
											<MaxData>C409</MaxData>
											<DefaultData>BC02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>MFC Velocity Feedforward Compensation</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>1027</MaxData>
											<DefaultData>E803</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>MFC Gain 2</Name>
										<Info>
											<MinData>0A00</MinData>
											<MaxData>204E</MaxData>
											<DefaultData>F401</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>MFC Gain Correction 2</Name>
										<Info>
											<MinData>F401</MinData>
											<MaxData>D007</MaxData>
											<DefaultData>E803</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Weak Field Control Gain</Name>
										<Info>
											<MinData>0A00</MinData>
											<MaxData>E803</MaxData>
											<DefaultData>1E00</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Weak Field Control Time Constant</Name>
										<Info>
											<MinData>0A00</MinData>
											<MaxData>B80B</MaxData>
											<DefaultData>3F00</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Weak Field Max Speed Corresponding To IdRef</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>E803</MaxData>
											<DefaultData>1C02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Weak Field Control Switch</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0100</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>AatJratEst</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0100</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>AatMode</Name>
										<Info>
											<MinData>0100</MinData>
											<MaxData>0400</MaxData>
											<DefaultData>0200</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>AatDistance</Name>
										<Info>
											<MinData>00000080</MinData>
											<MaxData>FFFFFF7F</MaxData>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>AatLoadType</Name>
										<Info>
											<MinData>0100</MinData>
											<MaxData>0300</MaxData>
											<DefaultData>0200</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>AatState</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>MotEstState</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
								</Info>
							</Object>
							<Object>
								<Index>#x2004</Index>
								<Name>Axis A Digital input parameter</Name>
								<Type>DT2004</Type>
								<BitSize>208</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>0C</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>DI1 Fuction Select</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>DI1 Logic Select</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0400</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>DI2 Fuction Select</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>DI2 Logic Select</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0400</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>DI3 Fuction Select</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>DI3 Logic Select</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0400</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>DI4 Fuction Select</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>DI4 Logic Select</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0400</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>DI5 Fuction Select</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>DI5 Logic Select</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0400</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>DI6 Fuction Select</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>DI6 Logic Select</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0400</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
								</Info>
							</Object>
							<Object>
								<Index>#x2005</Index>
								<Name>Axis A Digital output parameter</Name>
								<Type>DT2005</Type>
								<BitSize>208</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>0C</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>DO1 Fuction Select</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>DO1 Logic Select</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0100</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>DO2 Fuction Select</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>DO2 Logic Select</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0100</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>DO3 Fuction Select</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>DO3 Logic Select</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0100</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>DO4 Fuction Select</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>DO4 Logic Select</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0100</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>DO5 Fuction Select</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>DO5 Logic Select</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0100</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>DO6 Fuction Select</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>DO6 Logic Select</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0100</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
								</Info>
							</Object>
							<Object>
								<Index>#x2006</Index>
								<Name>Axis A Fault protection parameter</Name>
								<Type>DT2006</Type>
								<BitSize>496</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>17</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Vdc OV Level</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>D007</MaxData>
											<DefaultData>A401</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Vdc Discharge Level</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>D007</MaxData>
											<DefaultData>7C01</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Vdc Uv Level</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>D007</MaxData>
											<DefaultData>C800</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Vdc Uv Filter</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>D007</MaxData>
											<DefaultData>6400</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Vdc Uv Warn Level</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>D007</MaxData>
											<DefaultData>D200</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Over Speed Value</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>1027</MaxData>
											<DefaultData>9411</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PosErr Warn Level</Name>
										<Info>
											<MinData>0A000000</MinData>
											<MaxData>64000000</MaxData>
											<DefaultData>64000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>PosErr Alarm Level</Name>
										<Info>
											<MinData>01000000</MinData>
											<MaxData>FFFFFF3F</MaxData>
											<DefaultData>00000500</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Svon PosErr Warn Level</Name>
										<Info>
											<MinData>0A000000</MinData>
											<MaxData>64000000</MaxData>
											<DefaultData>64000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Svon PosErr Alarm Level</Name>
										<Info>
											<MinData>01000000</MinData>
											<MaxData>FFFFFF3F</MaxData>
											<DefaultData>00000500</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Over Run Detection Torque</Name>
										<Info>
											<MinData>0100</MinData>
											<MaxData>F401</MaxData>
											<DefaultData>7800</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Timestamp</Name>
										<Info>
											<MinData>00000000</MinData>
											<MaxData>FFFFFFFF</MaxData>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Alarm Mask</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Vibration Detection Sensitivity</Name>
										<Info>
											<MinData>3200</MinData>
											<MaxData>F401</MaxData>
											<DefaultData>6400</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Vibration Detection Value</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>B80B</MaxData>
											<DefaultData>3200</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Low Freq Vibration Detection Value </Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>B80B</MaxData>
											<DefaultData>FA00</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Vibration Detection Switch</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0200</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Remain Vibration Detection Width</Name>
										<Info>
											<MinData>0100</MinData>
											<MaxData>B80B</MaxData>
											<DefaultData>9001</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Position completion width</Name>
										<Info>
											<MinData>00000000</MinData>
											<MaxData>00000040</MaxData>
											<DefaultData>0A000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Alarm Cache chennal 1 setting</Name>
										<Info>
											<MinData>00000000</MinData>
											<MaxData>FFFFFFFF</MaxData>
											<DefaultData>03040507</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Alarm Cache chennal 2 setting</Name>
										<Info>
											<MinData>00000000</MinData>
											<MaxData>FFFFFFFF</MaxData>
											<DefaultData>08091113</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Alarm Cache chennal Time setting</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>FFFF</MaxData>
											<DefaultData>0200</DefaultData>
										</Info>
									</SubItem>
								</Info>
							</Object>
							<Object>
								<Index>#x2007</Index>
								<Name>Axis A Auxiliary Parameter</Name>
								<Type>DT2007</Type>
								<BitSize>224</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>0E</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>System Reset</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0100</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Factory Reset</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0100</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Fault Reset</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0100</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Clear Error History</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0100</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Phase Find Start</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0100</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Save Prm to Eeprom</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0100</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Load Prm from Eeprom</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0100</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Function Generater Start</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0100</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Fn Servo Off</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0100</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Motor Program Jog Command</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>1200</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Motor Jog Command</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>1200</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Get Motor Encoder Command</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0A00</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Auto Tunning</Name>
										<Info>
											<MinData>0000</MinData>
											<MaxData>0300</MaxData>
											<DefaultData>0000</DefaultData>
										</Info>
									</SubItem>
								</Info>
							</Object>
							<Object>
								<Index>#x6007</Index>
								<Name>Axis A CANopen abort option code</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<MinData>0080</MinData>
									<MaxData>FF7F</MaxData>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x603F</Index>
								<Name>Axis A Last error code</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>t</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6040</Index>
								<Name>Axis A Control word</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Info>
									<MinData>0000</MinData>
									<MaxData>FFFF</MaxData>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x6041</Index>
								<Name>Axis A Statusword</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>t</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x605A</Index>
								<Name>Axis A Quick stop option code</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<MinData>0080</MinData>
									<MaxData>FF7F</MaxData>
									<DefaultData>0200</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>t</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x605B</Index>
								<Name>Axis A Shutdown option code</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<MinData>0080</MinData>
									<MaxData>FF7F</MaxData>
									<DefaultData>0200</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x605C</Index>
								<Name>Axis A Disable operation option code</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<MinData>0080</MinData>
									<MaxData>FF7F</MaxData>
									<DefaultData>0100</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x605D</Index>
								<Name>Axis A Halt option code</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<MinData>0080</MinData>
									<MaxData>FF7F</MaxData>
									<DefaultData>0100</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x605E</Index>
								<Name>Axis A Fault Reaction Code</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<MinData>0080</MinData>
									<MaxData>FF7F</MaxData>
									<DefaultData>0100</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x6060</Index>
								<Name>Axis A Modes of operation</Name>
								<Type>SINT</Type>
								<BitSize>8</BitSize>
								<Info>
									<MinData>80</MinData>
									<MaxData>7F</MaxData>
									<DefaultData>08</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>r</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6061</Index>
								<Name>Axis A Modes of operation display</Name>
								<Type>SINT</Type>
								<BitSize>8</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>t</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6062</Index>
								<Name>Axis A Position demand value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>t</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6063</Index>
								<Name>Axis A Position actual internalvalue</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>t</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6064</Index>
								<Name>Axis A Position actual value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>t</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6065</Index>
								<Name>Axis A Following error window</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<MinData>00000000</MinData>
									<MaxData>FFFFFFFF</MaxData>
									<DefaultData>88130000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>r</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6066</Index>
								<Name>Axis A Following error timeout</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Info>
									<MinData>0000</MinData>
									<MaxData>FFFF</MaxData>
									<DefaultData>F401</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>r</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6067</Index>
								<Name>Axis A Position window</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<MinData>00000000</MinData>
									<MaxData>FFFFFFFF</MaxData>
									<DefaultData>64000000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>r</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6068</Index>
								<Name>Axis A Position window time</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Info>
									<MinData>0000</MinData>
									<MaxData>FFFF</MaxData>
									<DefaultData>C800</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>r</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6069</Index>
								<Name>Axis A Velocity sensor actual value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>t</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x606B</Index>
								<Name>Axis A Velocity demand value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>t</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x606C</Index>
								<Name>Axis A Velocity actual value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>t</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x606D</Index>
								<Name>Axis A Velocity window</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Info>
									<MinData>0000</MinData>
									<MaxData>FFFF</MaxData>
									<DefaultData>8813</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>r</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x606E</Index>
								<Name>Axis A Velocity window time</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Info>
									<MinData>0000</MinData>
									<MaxData>FFFF</MaxData>
									<DefaultData>6400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>r</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x606F</Index>
								<Name>Axis A Velocity threshold</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Info>
									<MinData>0000</MinData>
									<MaxData>FFFF</MaxData>
									<DefaultData>B80B</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>r</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6070</Index>
								<Name>Axis A Velocity threshold time</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Info>
									<MinData>0000</MinData>
									<MaxData>FFFF</MaxData>
									<DefaultData>6400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>r</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6071</Index>
								<Name>Axis A Target torque</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<MinData>0080</MinData>
									<MaxData>FF7F</MaxData>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>r</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6074</Index>
								<Name>Axis A Torque demand</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>t</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6077</Index>
								<Name>Axis A Torque actual value</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>t</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x607A</Index>
								<Name>Axis A Target position</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<MinData>00000080</MinData>
									<MaxData>FFFFFF7F</MaxData>
									<DefaultData>00000000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>r</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x607C</Index>
								<Name>Axis A Home offset</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<MinData>00000080</MinData>
									<MaxData>FFFFFF7F</MaxData>
									<DefaultData>00000000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>r</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x607D</Index>
								<Name>Axis A Position_range_limit</Name>
								<Type>DT607D</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Min software position limit</Name>
										<Info>
											<MinData>00000080</MinData>
											<MaxData>FFFFFF7F</MaxData>
											<DefaultData>00000080</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Max software position limit</Name>
										<Info>
											<MinData>00000080</MinData>
											<MaxData>FFFFFF7F</MaxData>
											<DefaultData>FFFFFF7F</DefaultData>
										</Info>
									</SubItem>
								</Info>
							</Object>
							<Object>
								<Index>#x607F</Index>
								<Name>Axis A Max profile velocity</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<MinData>00000000</MinData>
									<MaxData>FFFFFFFF</MaxData>
									<DefaultData>00006400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>r</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6081</Index>
								<Name>Axis A Profile velocity</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<MinData>00000000</MinData>
									<MaxData>FFFFFFFF</MaxData>
									<DefaultData>00001400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>r</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6082</Index>
								<Name>Axis A End Velocity</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<MinData>00000000</MinData>
									<MaxData>FFFFFFFF</MaxData>
									<DefaultData>00000000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>r</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6083</Index>
								<Name>Axis A Profile acceleration</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<MinData>00000000</MinData>
									<MaxData>FFFFFFFF</MaxData>
									<DefaultData>00006400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>r</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6084</Index>
								<Name>Axis A Profile deceleration</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<MinData>00000000</MinData>
									<MaxData>FFFFFFFF</MaxData>
									<DefaultData>00006400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>r</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6085</Index>
								<Name>Axis A Quick stop_deceleration</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<MinData>00000000</MinData>
									<MaxData>FFFFFFFF</MaxData>
									<DefaultData>00006400</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>r</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6086</Index>
								<Name>Axis A Motion profile type</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<MinData>0080</MinData>
									<MaxData>FF7F</MaxData>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>r</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6087</Index>
								<Name>Axis A Torque slope</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<MinData>00000000</MinData>
									<MaxData>FFFFFFFF</MaxData>
									<DefaultData>3C000000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>r</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6088</Index>
								<Name>Axis A Torque profile type</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<MinData>0080</MinData>
									<MaxData>FF7F</MaxData>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
								</Flags>
							</Object>
							<Object>
								<Index>#x6091</Index>
								<Name>Axis A Gear ratio</Name>
								<Type>DT6091</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Motor Revolutions</Name>
										<Info>
											<MinData>01000000</MinData>
											<MaxData>FFFFFFFF</MaxData>
											<DefaultData>01000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Load Shaft Revolutions</Name>
										<Info>
											<MinData>01000000</MinData>
											<MaxData>FFFFFFFF</MaxData>
											<DefaultData>01000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
							</Object>
							<Object>
								<Index>#x6092</Index>
								<Name>Axis A Feed constant</Name>
								<Type>DT6092</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Feed</Name>
										<Info>
											<MinData>01000000</MinData>
											<MaxData>FFFFFFFF</MaxData>
											<DefaultData>01000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Shaft Revolutions</Name>
										<Info>
											<MinData>01000000</MinData>
											<MaxData>FFFFFFFF</MaxData>
											<DefaultData>01000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
							</Object>
							<Object>
								<Index>#x6098</Index>
								<Name>Axis A Homing method</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<MinData>0080</MinData>
									<MaxData>FF7F</MaxData>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>r</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6099</Index>
								<Name>Axis A Homing speeds</Name>
								<Type>DT6099</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Fast Homing Speed</Name>
										<Info>
											<MinData>00000000</MinData>
											<MaxData>FFFFFFFF</MaxData>
											<DefaultData>50C30000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Slow Homing Speed</Name>
										<Info>
											<MinData>00000000</MinData>
											<MaxData>FFFFFFFF</MaxData>
											<DefaultData>204E0000</DefaultData>
										</Info>
									</SubItem>
								</Info>
							</Object>
							<Object>
								<Index>#x609A</Index>
								<Name>Axis A Homing acceleration</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<MinData>00000000</MinData>
									<MaxData>FFFFFFFF</MaxData>
									<DefaultData>50C30000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>r</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60A4</Index>
								<Name>Axis A Profile jerk</Name>
								<Type>DT60A4</Type>
								<BitSize>208</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>06</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Profile Jerk 1</Name>
										<Info>
											<MinData>00000080</MinData>
											<MaxData>FFFFFF7F</MaxData>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Profile Jerk 2</Name>
										<Info>
											<MinData>00000080</MinData>
											<MaxData>FFFFFF7F</MaxData>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Profile Jerk 3</Name>
										<Info>
											<MinData>00000080</MinData>
											<MaxData>FFFFFF7F</MaxData>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Profile Jerk 4</Name>
										<Info>
											<MinData>00000080</MinData>
											<MaxData>FFFFFF7F</MaxData>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Profile Jerk 5</Name>
										<Info>
											<MinData>00000080</MinData>
											<MaxData>FFFFFF7F</MaxData>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Profile Jerk 6</Name>
										<Info>
											<MinData>00000080</MinData>
											<MaxData>FFFFFF7F</MaxData>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
							</Object>
							<Object>
								<Index>#x60B0</Index>
								<Name>Axis A Position offset</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<MinData>00000080</MinData>
									<MaxData>FFFFFF7F</MaxData>
									<DefaultData>00000000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>r</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60B1</Index>
								<Name>Axis A Velocity offset</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<MinData>00000080</MinData>
									<MaxData>FFFFFF7F</MaxData>
									<DefaultData>00000000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>r</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60B2</Index>
								<Name>Axis A Torque offset</Name>
								<Type>INT</Type>
								<BitSize>16</BitSize>
								<Info>
									<MinData>0080</MinData>
									<MaxData>FF7F</MaxData>
									<DefaultData>0000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>r</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60C5</Index>
								<Name>Axis A Max acceleration</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<MinData>00000000</MinData>
									<MaxData>FFFFFFFF</MaxData>
									<DefaultData>0000E803</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>t</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60C6</Index>
								<Name>Axis A Max deceleration</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<MinData>00000000</MinData>
									<MaxData>FFFFFFFF</MaxData>
									<DefaultData>0000E803</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>t</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60E0</Index>
								<Name>Axis A Positive Torque Limit Value</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Info>
									<MinData>0000</MinData>
									<MaxData>FFFF</MaxData>
									<DefaultData>D007</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>r</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60E1</Index>
								<Name>Axis A Negative Torque Limit Value</Name>
								<Type>UINT</Type>
								<BitSize>16</BitSize>
								<Info>
									<MinData>0000</MinData>
									<MaxData>FFFF</MaxData>
									<DefaultData>D007</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>r</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60F4</Index>
								<Name>Axis A Following error actual value</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>r</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60FC</Index>
								<Name>Axis A Position demand internalvalue</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>t</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60FD</Index>
								<Name>Axis A  Digital Input</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<MinData>00000000</MinData>
									<MaxData>FFFFFFFF</MaxData>
									<DefaultData>00000000</DefaultData>
								</Info>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>t</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x60FE</Index>
								<Name>Axis A Digital Output</Name>
								<Type>DT60FE</Type>
								<BitSize>80</BitSize>
								<Info>
									<SubItem>
										<Name>SubIndex 000</Name>
										<Info>
											<DefaultData>02</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Physical Output</Name>
										<Info>
											<MinData>00000000</MinData>
											<MaxData>FFFFFFFF</MaxData>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
									<SubItem>
										<Name>Bit Mask</Name>
										<Info>
											<MinData>00000000</MinData>
											<MaxData>FFFFFFFF</MaxData>
											<DefaultData>00000000</DefaultData>
										</Info>
									</SubItem>
								</Info>
							</Object>
							<Object>
								<Index>#x60FF</Index>
								<Name>Axis A Target velocity</Name>
								<Type>DINT</Type>
								<BitSize>32</BitSize>
								<Info>
									<MinData>00000080</MinData>
									<MaxData>FFFFFF7F</MaxData>
									<DefaultData>00000000</DefaultData>
								</Info>
								<Flags>
									<Access>rw</Access>
									<PdoMapping>r</PdoMapping>
								</Flags>
							</Object>
							<Object>
								<Index>#x6502</Index>
								<Name>Supported Drive Modes</Name>
								<Type>UDINT</Type>
								<BitSize>32</BitSize>
								<Flags>
									<Access>ro</Access>
									<PdoMapping>r</PdoMapping>
								</Flags>
							</Object>									
						</Objects>
					</Dictionary>
				</Profile>
				<Fmmu>Outputs</Fmmu>
				<Fmmu>Inputs</Fmmu>
				<Fmmu>MBoxState</Fmmu>
				<Sm MinSize="#x24" MaxSize="#x80" DefaultSize="#x80" StartAddress="#x1000" ControlByte="#x26" Enable="1">MBoxOut</Sm>
				<Sm MinSize="#x24" MaxSize="#x80" DefaultSize="#x80" StartAddress="#x1080" ControlByte="#x22" Enable="1">MBoxIn</Sm>
				<Sm DefaultSize="12" StartAddress="#x1100" ControlByte="#x64" Enable="1">Outputs</Sm>
				<Sm DefaultSize="13" StartAddress="#x1400" ControlByte="#x20" Enable="1">Inputs</Sm>
				<RxPdo Fixed="false" Mandatory="false" Sm="2">
					<Index>#x1600</Index>
					<Name>RxPdo1 Axis A</Name>
					<Entry>
						<Index>#x6040</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Axis A Control word</Name>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index>#x607A</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Axis A Target position</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60B1</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Axis A Velocity offset</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x60B2</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Axis A Torque offset</Name>
						<DataType>INT</DataType>
					</Entry>
				</RxPdo>
				<TxPdo Fixed="false" Mandatory="false" Sm="3">
					<Index>#x1A00</Index>
					<Name>TxPdo1 Axis A</Name>
					<Entry>
						<Index>#x6041</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Axis A Statusword</Name>
						<DataType>UINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6064</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Axis A Position actual value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x606C</Index>
						<SubIndex>0</SubIndex>
						<BitLen>32</BitLen>
						<Name>Axis A Velocity actual value</Name>
						<DataType>DINT</DataType>
					</Entry>
					<Entry>
						<Index>#x6077</Index>
						<SubIndex>0</SubIndex>
						<BitLen>16</BitLen>
						<Name>Axis A Torque actual value</Name>
						<DataType>INT</DataType>
					</Entry>
					<Entry>
						<Index>#x6061</Index>
						<SubIndex>0</SubIndex>
						<BitLen>8</BitLen>
						<Name>Axis A Modes of operation display</Name>
						<DataType>SINT</DataType>
					</Entry>
				</TxPdo>
				<Mailbox DataLinkLayer="true">
					<CoE SdoInfo="true" PdoAssign="true" PdoConfig="true" CompleteAccess="true" SegmentedSdo="true">
						<InitCmd>
							<Transition>PS</Transition>
							<Index>#x6060</Index>
							<SubIndex>0</SubIndex>
							<Data>08</Data>
							<Comment>Op mode</Comment>
						</InitCmd>							
					</CoE>
				</Mailbox>
				<Dc>
					<OpMode>
						<Name>DC</Name>
						<Desc>DC-Synchron</Desc>
						<AssignActivate>#x300</AssignActivate>
						<CycleTimeSync0 Factor="1">0</CycleTimeSync0>
						<CycleTimeSync1 Factor="1">0</CycleTimeSync1>
					</OpMode>				
					<OpMode>
						<Name>Synchron</Name>
						<Desc>SM-Synchron</Desc>
						<AssignActivate>#x0</AssignActivate>
					</OpMode>
				</Dc>
				<Eeprom>
					<ByteSize>2048</ByteSize>
					<ConfigData>800C446610000000</ConfigData>
				</Eeprom>
                <ImageData16x14>424DD6020000000000003600000028000000100000000E000000010018000000000000000000C40E0000C40E00000000000000000000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFCFAF6F2E6D6F1E6D5F1E6D5F6EFE4FFFFFFF6EFE4F6EEE3F7F0E6F3E9D9FFFFFFF8F4EBF2E7D6F1E7D6F2E7D6FAF6F0FAF6F1C28C3CBC7E24BA7A1EBD8028F7F1E8C08632D1A86CD2A96DBF8631FAF7F1BB7E24B97717BC7E23C08835FBF9F6FFFFFFFCF9F5FBF9F5E2C9A3B77514F1E5D4C08532C8974FC48F41BF8631F1E5D4B77414E0C7A0FBF9F5FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE4CBA7B77514F1E5D4C08532C18734C08631BF8631F1E5D4B77414E5CFAFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE4CBA7B77514F1E5D3C08532D1A86DD2A96DBF8531F0E5D4B77414E5CFAFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF2E7D7E5CEADF9F5EEE6D0B0F1E6D6ECDDC6E9D7BCF5EEE2E4CEADF7F2E9FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
				</ImageData16x14>
			</Device>
		</Devices>
	</Descriptions>
</EtherCATInfo>